import { useState, useEffect, useCallback } from 'react';
import { Contract, formatEther, parseEther } from 'ethers';
import { CONTRACT_ADDRESSES, LAUNCHPAD_ABI } from '@/constants';
import { etherProvider } from '@/utils/helper';
import BigNumber from 'bignumber.js';
import { useDebounce } from './useDebounce';

export interface BondingCurveCalculation {
  // Input values
  inputAmount: string;

  // Calculated outputs
  tokensToReceive: string;
  totalCost: string;
  baseCost: string;
  platformFee: string;
  poolCreationFee: string;

  // State
  isCalculating: boolean;
  error: string | null;

  // Functions
  calculateFromInputAmount: (amount: string) => Promise<void>;
  calculateFromTokenAmount: (tokenAmount: string) => Promise<void>;
  reset: () => void;
}

interface ContractConfig {
  platformFee: string;
  initialVirtualTokenReserves: string;
  remainTokenReserves: string;
  tokenDecimals: number;
}

export const useBondingCurveCalculation = (): BondingCurveCalculation => {
  // State for calculation results
  const [inputAmount, setInputAmount] = useState<string>('');
  const [tokensToReceive, setTokensToReceive] = useState<string>('0');
  const [totalCost, setTotalCost] = useState<string>('0');
  const [baseCost, setBaseCost] = useState<string>('0');
  const [platformFee, setPlatformFee] = useState<string>('0');
  const [poolCreationFee] = useState<string>('0.001'); // Fixed pool creation fee

  // State management
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [contractConfig, setContractConfig] = useState<ContractConfig | null>(
    null,
  );

  // Debounce input to avoid excessive calculations
  const debouncedInputAmount = useDebounce(inputAmount, 300);

  // Initialize contract instance
  const launchpadContract = new Contract(
    CONTRACT_ADDRESSES.LAUNCHPAD,
    LAUNCHPAD_ABI,
    etherProvider,
  );

  // Fetch contract configuration on mount
  useEffect(() => {
    const fetchContractConfig = async () => {
      try {
        const config = await launchpadContract.config();

        setContractConfig({
          platformFee: config.platformFee.toString(),
          initialVirtualTokenReserves:
            config.initialVirtualTokenReserves.toString(),
          remainTokenReserves: config.remainTokenReserves.toString(),
          tokenDecimals: config.tokenDecimals,
        });
      } catch (error) {
        console.error('Error fetching contract config:', error);
        setError('Failed to load contract configuration');
      }
    };

    fetchContractConfig();
  }, []);

  // Calculate bonding curve values from input amount (HYPE amount)
  const calculateFromInputAmount = useCallback(
    async (amount: string) => {
      if (!amount || amount === '0' || !contractConfig) {
        setTokensToReceive('0');
        setTotalCost('0');
        setBaseCost('0');
        setPlatformFee('0');
        setError(null);
        return;
      }

      // Validate input amount
      const numAmount = parseFloat(amount);
      if (numAmount <= 0) {
        setError('Amount must be greater than 0');
        return;
      }

      if (numAmount > 1000) {
        setError('Amount too large (max 1000 HYPE)');
        return;
      }

      setIsCalculating(true);
      setError(null);

      try {
        const amountInWei = parseEther(amount);

        // For new pool creation, we simulate the calculation using the virtual reserves
        // Since the pool doesn't exist yet, we calculate based on initial state

        // Get virtual reserves for a new pool
        const virtualTokenReserves =
          await launchpadContract.calculateActualVirtualTokenReserves();
        const virtualRemainTokenReserves =
          await launchpadContract.calculateVirtualRemainTokenReserves();
        const virtualHypeReserves =
          await launchpadContract.calculateInitialVirtualHypeReserves(0); // 0 for default threshold

        // Calculate available tokens in pool
        const tokenReservesInPool = BigNumber(
          virtualTokenReserves.toString(),
        ).minus(virtualRemainTokenReserves.toString());

        // Use bonding curve formula to calculate tokens out
        // Formula: tokensOut = virtualTokenReserves - (virtualHypeReserves * virtualTokenReserves) / (virtualHypeReserves + amountIn)
        const amountInBN = BigNumber(amountInWei.toString());
        const virtualHypeReservesBN = BigNumber(virtualHypeReserves.toString());
        const virtualTokenReservesBN = BigNumber(
          virtualTokenReserves.toString(),
        );

        // Calculate tokens received
        const denominator = virtualHypeReservesBN.plus(amountInBN);
        const tokensOut = virtualTokenReservesBN.minus(
          virtualHypeReservesBN
            .multipliedBy(virtualTokenReservesBN)
            .dividedBy(denominator),
        );

        // Ensure we don't exceed available tokens
        const actualTokensOut = BigNumber.min(tokensOut, tokenReservesInPool);

        // Validate that we can actually get tokens
        if (actualTokensOut.isLessThanOrEqualTo(0)) {
          setError('No tokens available for this amount');
          return;
        }

        // Calculate the actual cost for these tokens using reverse formula
        // baseCost = (virtualHypeReserves * virtualTokenReserves) / (virtualTokenReserves - tokensOut) - virtualHypeReserves + 1
        const baseCostBN = virtualHypeReservesBN
          .multipliedBy(virtualTokenReservesBN)
          .dividedBy(virtualTokenReservesBN.minus(actualTokensOut))
          .minus(virtualHypeReservesBN)
          .plus(1);

        // Validate calculation results
        if (baseCostBN.isNaN() || baseCostBN.isLessThan(0)) {
          setError('Invalid calculation result');
          return;
        }

        // Calculate platform fee (percentage of base cost)
        const platformFeeBN = baseCostBN
          .multipliedBy(contractConfig.platformFee)
          .dividedBy(10000); // FEE_DENOMINATOR = 10,000

        // Total cost includes base cost + platform fee + pool creation fee
        const poolCreationFeeBN = BigNumber(
          parseEther(poolCreationFee).toString(),
        );
        const totalCostBN = baseCostBN
          .plus(platformFeeBN)
          .plus(poolCreationFeeBN);

        // Update state with calculated values
        setTokensToReceive(formatEther(actualTokensOut.toFixed(0)));
        setBaseCost(formatEther(baseCostBN.toFixed(0)));
        setPlatformFee(formatEther(platformFeeBN.toFixed(0)));
        setTotalCost(formatEther(totalCostBN.toFixed(0)));
      } catch (error) {
        console.error('Error calculating bonding curve:', error);
        setError('Failed to calculate bonding curve values');
        setTokensToReceive('0');
        setTotalCost('0');
        setBaseCost('0');
        setPlatformFee('0');
      } finally {
        setIsCalculating(false);
      }
    },
    [contractConfig, poolCreationFee],
  );

  // Calculate required input amount from desired token amount
  const calculateFromTokenAmount = useCallback(
    async (tokenAmount: string) => {
      if (!tokenAmount || tokenAmount === '0' || !contractConfig) {
        setInputAmount('0');
        setTotalCost('0');
        setBaseCost('0');
        setPlatformFee('0');
        setError(null);
        return;
      }

      setIsCalculating(true);
      setError(null);

      try {
        const tokenAmountInWei = parseEther(tokenAmount);

        // Calculate virtual reserves for a new pool
        const virtualTokenReserves =
          await launchpadContract.calculateActualVirtualTokenReserves();
        const virtualRemainTokenReserves =
          await launchpadContract.calculateVirtualRemainTokenReserves();
        const virtualHypeReserves =
          await launchpadContract.calculateInitialVirtualHypeReserves(0);

        // Calculate token reserves in pool
        const tokenReservesInPool = BigNumber(
          virtualTokenReserves.toString(),
        ).minus(virtualRemainTokenReserves.toString());

        const tokenAmountBN = BigNumber(tokenAmountInWei.toString());

        // Ensure requested amount doesn't exceed available tokens
        if (tokenAmountBN.isGreaterThan(tokenReservesInPool)) {
          setError('Requested token amount exceeds available supply');
          return;
        }

        const virtualHypeReservesBN = BigNumber(virtualHypeReserves.toString());
        const virtualTokenReservesBN = BigNumber(
          virtualTokenReserves.toString(),
        );

        // Calculate base cost using bonding curve formula
        const baseCostBN = virtualHypeReservesBN
          .multipliedBy(virtualTokenReservesBN)
          .dividedBy(virtualTokenReservesBN.minus(tokenAmountBN))
          .minus(virtualHypeReservesBN)
          .plus(1);

        // Calculate platform fee (percentage of base cost)
        const platformFeeBN = baseCostBN
          .multipliedBy(contractConfig.platformFee)
          .dividedBy(10000); // FEE_DENOMINATOR = 10,000

        // Total cost = base cost + platform fee + pool creation fee
        const poolCreationFeeBN = BigNumber(
          parseEther(poolCreationFee).toString(),
        );
        const totalCostBN = baseCostBN
          .plus(platformFeeBN)
          .plus(poolCreationFeeBN);

        // Update state
        setInputAmount(formatEther(baseCostBN.plus(platformFeeBN).toFixed(0)));
        setBaseCost(formatEther(baseCostBN.toFixed(0)));
        setPlatformFee(formatEther(platformFeeBN.toFixed(0)));
        setTotalCost(formatEther(totalCostBN.toFixed(0)));
        setTokensToReceive(tokenAmount);
      } catch (error) {
        console.error('Error calculating required input:', error);
        setError('Failed to calculate required input amount');
        setInputAmount('0');
        setTotalCost('0');
        setBaseCost('0');
        setPlatformFee('0');
      } finally {
        setIsCalculating(false);
      }
    },
    [contractConfig, poolCreationFee],
  );

  // Reset all calculations
  const reset = useCallback(() => {
    setInputAmount('');
    setTokensToReceive('0');
    setTotalCost('0');
    setBaseCost('0');
    setPlatformFee('0');
    setError(null);
  }, []);

  // Auto-calculate when debounced input changes
  useEffect(() => {
    if (debouncedInputAmount && debouncedInputAmount !== '0') {
      calculateFromInputAmount(debouncedInputAmount);
    }
  }, [debouncedInputAmount, calculateFromInputAmount]);

  return {
    // Input values
    inputAmount,

    // Calculated outputs
    tokensToReceive,
    totalCost,
    baseCost,
    platformFee,
    poolCreationFee,

    // State
    isCalculating,
    error,

    // Functions
    calculateFromInputAmount,
    calculateFromTokenAmount,
    reset,
  };
};
