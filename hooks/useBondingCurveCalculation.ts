import { useState, useCallback } from 'react';
import { Contract, formatEther, parseEther, Interface } from 'ethers';
import { CONTRACT_ADDRESSES, LAUNCHPAD_ABI, MULTICALL_ABI } from '@/constants';
import { etherProvider } from '@/utils/helper';

export interface BondingCurveCalculation {
  // Calculated outputs
  tokensToReceive: string;

  // State
  isCalculating: boolean;
  error: string | null;

  // Functions
  calculateFromInputAmount: (amount: string) => Promise<void>;
  reset: () => void;
}

export const useBondingCurveCalculation = (): BondingCurveCalculation => {
  // State for calculation results
  const [tokensToReceive, setTokensToReceive] = useState<string>('0');

  // State management
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize contract instances
  const multicallContract = new Contract(
    CONTRACT_ADDRESSES.MULTICALL,
    MULTICALL_ABI,
    etherProvider,
  );

  const launchpadInterface = new Interface(LAUNCHPAD_ABI);

  // Calculate tokens to receive for a given input amount using contract estimation
  const calculateFromInputAmount = useCallback(
    async (amount: string) => {
      if (!amount || amount === '0') {
        setTokensToReceive('0');
        setError(null);
        return;
      }

      // Validate input amount
      const numAmount = parseFloat(amount);
      if (numAmount <= 0) {
        setError('Amount must be greater than 0');
        return;
      }

      setIsCalculating(true);
      setError(null);

      try {
        const amountInWei = parseEther(amount);

        // Use multicall to batch all required contract calls
        const calls = [
          {
            target: CONTRACT_ADDRESSES.LAUNCHPAD,
            callData: launchpadInterface.encodeFunctionData(
              'calculateActualVirtualTokenReserves',
            ),
          },
          {
            target: CONTRACT_ADDRESSES.LAUNCHPAD,
            callData: launchpadInterface.encodeFunctionData(
              'calculateVirtualRemainTokenReserves',
            ),
          },
          {
            target: CONTRACT_ADDRESSES.LAUNCHPAD,
            callData: launchpadInterface.encodeFunctionData(
              'calculateInitialVirtualHypeReserves',
              [0],
            ),
          },
        ];

        const [, returnData] = await multicallContract.aggregate(calls);

        // Decode the results
        const virtualTokenReserves = launchpadInterface.decodeFunctionResult(
          'calculateActualVirtualTokenReserves',
          returnData[0],
        )[0];

        const virtualRemainTokenReserves =
          launchpadInterface.decodeFunctionResult(
            'calculateVirtualRemainTokenReserves',
            returnData[1],
          )[0];

        const virtualHypeReserves = launchpadInterface.decodeFunctionResult(
          'calculateInitialVirtualHypeReserves',
          returnData[2],
        )[0];

        // Calculate available tokens in pool
        const tokenReservesInPool =
          virtualTokenReserves - virtualRemainTokenReserves;

        // Use the bonding curve formula to calculate tokens out
        // tokensOut = virtualTokenReserves - (virtualHypeReserves * virtualTokenReserves) / (virtualHypeReserves + amountIn)
        const denominator = virtualHypeReserves + amountInWei;
        const tokensOut =
          virtualTokenReserves -
          (virtualHypeReserves * virtualTokenReserves) / denominator;

        // Ensure we don't exceed available tokens
        const actualTokensOut =
          tokensOut > tokenReservesInPool ? tokenReservesInPool : tokensOut;

        if (actualTokensOut <= 0) {
          setError('No tokens available for this amount');
          return;
        }

        console.log(actualTokensOut, 'actualTokensOut');

        // Update state with calculated tokens
        setTokensToReceive(formatEther(actualTokensOut.toString()));
      } catch (error) {
        console.error('Error calculating bonding curve:', error);
        setError('Failed to calculate token amount');
        setTokensToReceive('0');
      } finally {
        setIsCalculating(false);
      }
    },
    [multicallContract, launchpadInterface],
  );

  // Reset all calculations
  const reset = useCallback(() => {
    setTokensToReceive('0');
    setError(null);
  }, []);

  return {
    // Calculated outputs
    tokensToReceive,

    // State
    isCalculating,
    error,

    // Functions
    calculateFromInputAmount,
    reset,
  };
};
