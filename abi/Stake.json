[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin_", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "name": "changeAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "implementation_", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}, {"inputs": [], "name": "AccountBalanceNotEnough", "type": "error"}, {"inputs": [], "name": "CreatorPoolAlreadyExists", "type": "error"}, {"inputs": [], "name": "InvalidAmount", "type": "error"}, {"inputs": [], "name": "InvalidCreator", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidTokenAddress", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "NotUpgrade", "type": "error"}, {"inputs": [], "name": "Only<PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [], "name": "RewardToClaimNotValid", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "StakingAccountNotExist", "type": "error"}, {"inputs": [], "name": "StakingCreatorNotExist", "type": "error"}, {"inputs": [], "name": "StakingPoolAlreadyExists", "type": "error"}, {"inputs": [], "name": "StakingPoolNotExist", "type": "error"}, {"inputs": [], "name": "UnstakeDeadlineNotAllow", "type": "error"}, {"inputs": [], "name": "ZeroAddress", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "claimer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ClaimCreatorPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "claimer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isStakingAccountDeleted", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ClaimStakingPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "depositor", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "DepositPoolCreatorEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "initializer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "InitializeCreatorPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "initializer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "InitializeStakingPoolEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "staker", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "StakeEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "unstaker", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isStakingAccountDeleted", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UnstakeEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "tokenAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "rewardUpdater", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isInitialRewards", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "UpdateRewardIndexEvent", "type": "event"}, {"inputs": [], "name": "DEFAULT_DENY_UNSTAKE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "address", "name": "userAddress", "type": "address"}], "name": "calculateRewardsEarnedForUser", "outputs": [{"internalType": "uint256", "name": "totalEarned", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "claimCreatorPool", "outputs": [{"internalType": "uint256", "name": "rewardAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "claimStakingPool", "outputs": [{"internalType": "uint256", "name": "rewardAmount", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "uint256", "name": "denyUnstakeDuration", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "creatorPoolExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "creatorPools", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "depositCreatorPool", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "getCreatorPoolInfo", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "address", "name": "staker", "type": "address"}], "name": "getStakingAccountInfo", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "earned", "type": "uint256"}, {"internalType": "uint256", "name": "unstakeDeadline", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "getStakingPoolInfo", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "pendingInitialRewards", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "address", "name": "creator", "type": "address"}], "name": "initializeCreatorPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "initializeStakingPool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "user", "type": "address"}], "name": "stakingAccountExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}], "name": "stakingAccounts", "outputs": [{"internalType": "address", "name": "staker", "type": "address"}, {"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "earned", "type": "uint256"}, {"internalType": "uint256", "name": "unstakeDeadline", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "stakingPoolExists", "outputs": [{"internalType": "bool", "name": "exists", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "stakingPools", "outputs": [{"internalType": "address", "name": "initializer", "type": "address"}, {"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "uint256", "name": "rewardIndex", "type": "uint256"}, {"internalType": "uint256", "name": "pendingInitialRewards", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}, {"internalType": "uint256", "name": "unstakeAmount", "type": "uint256"}], "name": "unstake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}, {"internalType": "uint256", "name": "newDenyUnstakeDuration", "type": "uint256"}], "name": "updateConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "stakingToken", "type": "address"}], "name": "updateRewardIndex", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "constructor"}]