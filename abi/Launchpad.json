[{"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "previousAdmin", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newAdmin", "type": "address"}], "name": "Admin<PERSON><PERSON>ed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beacon", "type": "address"}], "name": "BeaconUpgraded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "implementation", "type": "address"}], "name": "Upgraded", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin_", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newAdmin", "type": "address"}], "name": "changeAdmin", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "implementation", "outputs": [{"internalType": "address", "name": "implementation_", "type": "address"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}], "name": "upgradeTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "upgradeToAndCall", "outputs": [], "stateMutability": "payable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}, {"inputs": [], "name": "InsufficientAmount", "type": "error"}, {"inputs": [], "name": "InsufficientHypeReserves", "type": "error"}, {"inputs": [], "name": "InsufficientReserves", "type": "error"}, {"inputs": [], "name": "InsufficientTokenReserves", "type": "error"}, {"inputs": [], "name": "InvalidConfiguration", "type": "error"}, {"inputs": [], "name": "InvalidDistributionTime", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidWithdrawalAmount", "type": "error"}, {"inputs": [], "name": "LPValueDecreased", "type": "error"}, {"inputs": [], "name": "NoPositionFound", "type": "error"}, {"inputs": [], "name": "NotEnoughThreshold", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "PoolAlreadyCompleted", "type": "error"}, {"inputs": [], "name": "PoolNotCompleted", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "TokenNotExists", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPlatformFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newInitialVirtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRemainTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint8", "name": "newTokenDecimals", "type": "uint8"}, {"indexed": false, "internalType": "uint16", "name": "newInitPlatformFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitCreatorFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "newInitPlatformStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "address", "name": "newPlatformTokenAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "ConfigurationUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "string", "name": "lp", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PoolCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "wethAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint24", "name": "feeTier", "type": "uint24"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "PoolMigratingEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "creator", "type": "address"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "string", "name": "symbol", "type": "string"}, {"indexed": false, "internalType": "string", "name": "uri", "type": "string"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}, {"indexed": false, "internalType": "string", "name": "twitter", "type": "string"}, {"indexed": false, "internalType": "string", "name": "telegram", "type": "string"}, {"indexed": false, "internalType": "string", "name": "website", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"indexed": false, "internalType": "uint256", "name": "threshold", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "TokenCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "bool", "name": "isBuy", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "hypeAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "Trade", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": true, "internalType": "address", "name": "v3Pool", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "V3PoolCreated", "type": "event"}, {"inputs": [], "name": "activeFeeTier", "outputs": [{"internalType": "uint24", "name": "", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "bondingDeployer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "name": "buyExactIn", "outputs": [{"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "calculateActualVirtualTokenReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "name": "calculateInitialVirtualHypeReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "calculateVirtualRemainTokenReserves", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "collectHyperSwapFees", "outputs": [{"internalType": "uint256", "name": "amount0", "type": "uint256"}, {"internalType": "uint256", "name": "amount1", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "config", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}, {"internalType": "address", "name": "treasury", "type": "address"}, {"internalType": "address", "name": "feePlatformRecipient", "type": "address"}, {"internalType": "uint256", "name": "platformFee", "type": "uint256"}, {"internalType": "uint256", "name": "initialVirtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint8", "name": "tokenDecimals", "type": "uint8"}, {"internalType": "uint16", "name": "initPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "initPlatformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "address", "name": "platformTokenAddress", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "twitter", "type": "string"}, {"internalType": "string", "name": "telegram", "type": "string"}, {"internalType": "string", "name": "website", "type": "string"}, {"internalType": "uint256", "name": "customThreshold", "type": "uint256"}, {"internalType": "uint256", "name": "amountOut", "type": "uint256"}, {"internalType": "uint256", "name": "lockDuration", "type": "uint256"}], "name": "createAndLockFirstBuy", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "twitter", "type": "string"}, {"internalType": "string", "name": "telegram", "type": "string"}, {"internalType": "string", "name": "website", "type": "string"}, {"internalType": "uint256", "name": "customThreshold", "type": "uint256"}], "name": "createPool", "outputs": [{"internalType": "address", "name": "tokenAddress", "type": "address"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "createThresholdConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "distributeBondingCurveFees", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "earlyCompletePool", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "estimateBuyExactInCost", "outputs": [{"internalType": "uint256", "name": "totalCost", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amountIn", "type": "uint256"}], "name": "estimateBuyExactInTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountOut", "type": "uint256"}], "name": "estimateBuyExactOutCost", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256"}], "name": "estimateSellTokens", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getHyperSwapCollectFees", "outputs": [{"internalType": "uint128", "name": "tokenAmount", "type": "uint128"}, {"internalType": "uint128", "name": "wethAmount", "type": "uint128"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "getPool", "outputs": [{"components": [{"internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualRemainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "feeRecipient", "type": "uint256"}, {"internalType": "bool", "name": "isCompleted", "type": "bool"}, {"internalType": "uint256", "name": "threshold", "type": "uint256"}, {"internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint256", "name": "creationTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "feeDistributionUnlockTime", "type": "uint256"}], "internalType": "struct PrintMemeEVMLaunchpad.Pool", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_nonfungiblePositionManager", "type": "address"}, {"internalType": "address", "name": "_weth9", "type": "address"}, {"internalType": "uint24", "name": "_poolFee", "type": "uint24"}, {"internalType": "address", "name": "_platformTokenAddress", "type": "address"}, {"internalType": "address", "name": "_printMemeEVMStake", "type": "address"}, {"internalType": "address", "name": "_tokenLock", "type": "address"}, {"internalType": "address", "name": "_bondingDeployer", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "isToken", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmount", "type": "uint256"}, {"internalType": "uint256", "name": "hypeAmount", "type": "uint256"}, {"internalType": "uint24", "name": "feeTier", "type": "uint24"}], "name": "migrateToHyperSwapV3", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "nonfungiblePositionManager", "outputs": [{"internalType": "contract INonfungiblePositionManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "pools", "outputs": [{"internalType": "uint256", "name": "realHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "realTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualHypeReserves", "type": "uint256"}, {"internalType": "uint256", "name": "remainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "virtualRemainTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "feeRecipient", "type": "uint256"}, {"internalType": "bool", "name": "isCompleted", "type": "bool"}, {"internalType": "uint256", "name": "threshold", "type": "uint256"}, {"internalType": "uint16", "name": "platformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint16"}, {"internalType": "uint16", "name": "stakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "platformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint256", "name": "creationTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "feeDistributionUnlockTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "printMemeEVMStake", "outputs": [{"internalType": "contract PrintMemeEVMStake", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "tokenAmountIn", "type": "uint256"}, {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"}], "name": "sellExactIn", "outputs": [{"internalType": "uint256", "name": "hypeAmountOut", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_bondingDeployer", "type": "address"}], "name": "setBondingDeployer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_printMemeEVMStake", "type": "address"}], "name": "setPrintMemeEVMStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenLock", "type": "address"}], "name": "setTokenLock", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "skim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "thresholdConfig", "outputs": [{"internalType": "uint256", "name": "threshold", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenLock", "outputs": [{"internalType": "contract TokenLock", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokenToPositionId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint24", "name": "newFeeTier", "type": "uint24"}], "name": "updateActiveFeeTier", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint16", "name": "_newInitPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_newInitPlatformStakeFeeWithdraw", "type": "uint16"}], "name": "updateConfigWithdrawFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_platformFee", "type": "uint256"}, {"internalType": "uint256", "name": "_initialVirtualTokenReserves", "type": "uint256"}, {"internalType": "uint256", "name": "_remainTokenReserves", "type": "uint256"}, {"internalType": "uint8", "name": "_tokenDecimals", "type": "uint8"}, {"internalType": "uint16", "name": "_initPlatformFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initCreatorFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initStakeFeeWithdraw", "type": "uint16"}, {"internalType": "uint16", "name": "_initPlatformStakeFeeWithdraw", "type": "uint16"}, {"internalType": "address", "name": "_platformTokenAddress", "type": "address"}], "name": "updateConfiguration", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_treasury", "type": "address"}, {"internalType": "address", "name": "_feePlatformRecipient", "type": "address"}], "name": "updateFeeRecipients", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_initialVirtualTokenReserves", "type": "uint256"}], "name": "updateInitialVirtualTokenReserves", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}], "name": "updateThresholdConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "weth9", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_logic", "type": "address"}, {"internalType": "address", "name": "admin_", "type": "address"}, {"internalType": "bytes", "name": "_data", "type": "bytes"}], "stateMutability": "payable", "type": "constructor"}]