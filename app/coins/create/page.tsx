'use client';
import { TelegramIcon, TwitterIcon, WebsiteIcon } from '@/assets/icons';
import { HyperImg } from '@/assets/images';
import { AppButton } from '@/components/AppButton';
import AppInput from '@/components/AppInput';
import AppTextarea from '@/components/AppTextarea';
import { errorMsg, successMsg } from '@/libs/toast';
import { TELEGRAM_REGEX, TWITTER_REGEX, WEBSITE_REGEX } from '@/utils/helper';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import UploadMedia from './_components/UploadMedia';
import BondingCurveDisplay from './_components/BondingCurveDisplay';
import { usePrivy } from '@privy-io/react-auth';
import LaunchpadAbi from '@/abi/Launchpad.json';
import config from '@/config';
import { useSigner } from '@/hooks/useSigner';
import { useBondingCurveCalculation } from '@/hooks/useBondingCurveCalculation';
import { Contract, parseEther } from 'ethers';
import { useRouter } from 'next/navigation';
import BigNumber from 'bignumber.js';

const CreateCoin = () => {
  const { authenticated, login } = usePrivy();

  const [preview, setPreview] = useState<any>();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const { signer } = useSigner();
  const { control, handleSubmit, watch } = useForm({
    mode: 'onTouched',
    defaultValues: {
      name: '',
      ticker: '',
      description: '',
      telegram: '',
      website: '',
      twitter: '',
      buyFirst: '',
    },
  });

  const router = useRouter();

  // Watch the buyFirst field for real-time calculations
  const buyFirstValue = watch('buyFirst');

  // Initialize bonding curve calculation hook
  const {
    tokensToReceive,
    totalCost,
    baseCost,
    platformFee,
    poolCreationFee,
    isCalculating,
    error: calculationError,
    calculateFromInputAmount,
  } = useBondingCurveCalculation();

  // Calculate bonding curve when buyFirst value changes
  useEffect(() => {
    if (buyFirstValue && buyFirstValue !== '0') {
      calculateFromInputAmount(buyFirstValue);
    }
  }, [buyFirstValue, calculateFromInputAmount]);

  const onSubmit = handleSubmit(
    async (data) => {
      setLoading(true);
      try {
        const {
          name,
          ticker,
          description,
          website,
          twitter,
          telegram,
          buyFirst,
        } = data;

        if (!imageUrl) {
          errorMsg('Please upload an image first');
          setLoading(false);
          return;
        }

        const contract = new Contract(
          config?.contracts?.launchpadAddress || '',
          LaunchpadAbi,
          signer,
        );

        // Prepare transaction options
        const txOptions: any = {};

        // If user wants to buy tokens on creation, include the total cost as value
        if (BigNumber(buyFirst || 0).isGreaterThan(0)) {
          if (!totalCost || totalCost === '0') {
            errorMsg('Please wait for calculation to complete');
            setLoading(false);
            return;
          }

          // Use the calculated total cost (includes base cost + platform fee + pool creation fee)
          txOptions.value = parseEther(totalCost);

          console.log('Creating pool with initial purchase:', {
            buyFirstAmount: buyFirst,
            totalCost: totalCost,
            tokensToReceive: tokensToReceive,
          });
        } else {
          // Just pool creation fee if no initial purchase
          txOptions.value = parseEther('0.001');
        }

        const res = await contract.createPool(
          name,
          ticker,
          imageUrl, // Use the uploaded image URL
          description,
          twitter || '',
          telegram || '',
          website || '',
          0, // customThreshold
          txOptions,
        );

        await res.wait();
        router.push('/');
        successMsg('Coin created successfully!');
        console.log('Transaction result:', res);
      } catch (error: any) {
        errorMsg(error?.message || 'Something went wrong');
      } finally {
        setLoading(false);
      }
    },
    (error) => {
      console.log('error', error);
      setLoading(false);
    },
  );

  return (
    <div className="relative w-full h-full flex justify-center items-center">
      <div className="hidden md:block absolute top-0 right-0 left-0 bg-[url('../assets/images/banner.png')] aspect-[1440/640] bg-cover z-10"></div>

      <div className="p-6 max-w-[500px] flex justify-center items-top bg-black-800 md:border-[2px] border-white-100 rounded-[4px] z-[15] mt-[20px] mb-[100px] md:my-[100px] backdrop-blur-md">
        <form className="space-y-6" onSubmit={onSubmit}>
          <Controller
            name="name"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Name"
                required
                placeholder="Enter token name here"
                error={formState?.errors?.name?.message}
              />
            )}
          />

          <Controller
            name="ticker"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Ticker"
                required
                placeholder="Enter the token ticker"
                icon={<div className="text-white-0 text-[12px]">$</div>}
                error={formState?.errors?.ticker?.message}
              />
            )}
          />

          <Controller
            name="description"
            control={control}
            rules={{
              required: 'This field is required.',
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
            }}
            render={({ field, formState }) => (
              <AppTextarea
                {...field}
                required
                label="Description"
                placeholder="Enter text here"
                error={formState?.errors?.description?.message}
              />
            )}
          />

          <div className="flex gap-[12px] md:gap-[24px]">
            <div className="w-1/2">
              <UploadMedia
                preview={preview}
                setPreview={setPreview}
                setImageUrl={setImageUrl}
              />
            </div>
            <div className="w-1/2 space-y-4">
              <Controller
                name="website"
                control={control}
                rules={{
                  pattern: {
                    value: WEBSITE_REGEX,
                    message:
                      'Please enter a valid website link (e.g. https://example.com)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Website"
                    placeholder="Enter link"
                    icon={<WebsiteIcon />}
                    error={formState?.errors?.website?.message}
                  />
                )}
              />
              <Controller
                name="twitter"
                control={control}
                rules={{
                  pattern: {
                    value: TWITTER_REGEX,
                    message:
                      'Please enter a valid Twitter or X link (e.g. https://twitter.com/username)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Twitter"
                    placeholder="Enter link"
                    icon={<TwitterIcon />}
                    error={formState?.errors?.twitter?.message}
                  />
                )}
              />
              <Controller
                name="telegram"
                control={control}
                rules={{
                  pattern: {
                    value: TELEGRAM_REGEX,
                    message:
                      'Please enter a valid Telegram link (e.g. https://t.me/username)',
                  },
                }}
                render={({ field, formState }) => (
                  <AppInput
                    {...field}
                    label="Telegram"
                    placeholder="Enter link"
                    icon={<TelegramIcon />}
                    error={formState?.errors?.telegram?.message}
                  />
                )}
              />
            </div>
          </div>

          <div className="w-full h-[1px] bg-white-100"></div>

          <Controller
            name="buyFirst"
            control={control}
            rules={{
              maxLength: {
                value: 500,
                message: 'Max 500 characters',
              },
              validate: {
                isValidNumber: (value) => {
                  if (!value) return true; // Optional field
                  const num = parseFloat(value);
                  if (isNaN(num)) return 'Please enter a valid number';
                  if (num < 0) return 'Amount must be positive';
                  if (num > 1000) return 'Amount too large';
                  return true;
                },
                hasNoCalculationError: () => {
                  if (
                    buyFirstValue &&
                    buyFirstValue !== '0' &&
                    calculationError
                  ) {
                    return calculationError;
                  }
                  return true;
                },
              },
            }}
            render={({ field, formState }) => (
              <AppInput
                {...field}
                label="Buy First"
                placeholder="Enter HYPE amount"
                iconPosition="end"
                icon={
                  <div className="text-white-300 text-[12px] flex items-center gap-2 mr-2">
                    HYPE
                    <Image
                      src={HyperImg}
                      width={14}
                      height={14}
                      alt="Hyperliquid"
                    />
                  </div>
                }
                error={formState?.errors?.buyFirst?.message}
              />
            )}
          />

          {/* Bonding Curve Calculation Display */}
          <BondingCurveDisplay
            tokensToReceive={tokensToReceive}
            totalCost={totalCost}
            baseCost={baseCost}
            platformFee={platformFee}
            poolCreationFee={poolCreationFee}
            isCalculating={isCalculating}
            error={calculationError}
            inputAmount={buyFirstValue}
          />

          {!!authenticated ? (
            <AppButton
              className="w-full bg-brand-500 py-4 !rounded-[2px] hover:scale-[1.02] transition-all duration-300"
              isLoading={loading}
            >
              {loading ? 'Printing...' : 'Print Coin'}
            </AppButton>
          ) : (
            <AppButton
              type="button"
              className="w-full bg-brand-500 py-4 !rounded-[2px] hover:scale-[1.02] transition-all duration-300"
              onClick={() => login()}
            >
              Connect your wallet to print coin
            </AppButton>
          )}
        </form>
      </div>
    </div>
  );
};

export default CreateCoin;
