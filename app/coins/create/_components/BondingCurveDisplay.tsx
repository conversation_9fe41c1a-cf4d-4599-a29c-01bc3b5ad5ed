'use client';
import BigNumber from 'bignumber.js';

interface BondingCurveDisplayProps {
  tokensToReceive: string;
  isCalculating: boolean;
  error: string | null;
  inputAmount: string;
}

const BondingCurveDisplay = ({
  tokensToReceive,
  isCalculating,
  error,
  inputAmount,
}: BondingCurveDisplayProps) => {
  // Don't show if no input amount
  if (!inputAmount || inputAmount === '0') {
    return null;
  }

  return (
    <div className="bg-black-700 border border-white-100 rounded-[4px] p-4 space-y-3">
      <div className="text-white-0 text-sm font-medium">Purchase Preview</div>

      {isCalculating ? (
        <div className="text-white-300 text-sm flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white-300"></div>
          Calculating...
        </div>
      ) : error ? (
        <div className="text-red-500 text-sm bg-red-500/10 border border-red-500/20 rounded p-2">
          {error}
        </div>
      ) : (
        <div className="space-y-3">
          <div className="bg-black-700 rounded p-3">
            <div className="text-white-0 font-medium mb-2">
              Input: {inputAmount} HYPE
            </div>
            <div className="text-brand-400 font-medium text-lg">
              You will receive: {BigNumber(tokensToReceive).toFormat(2)} tokens
            </div>
          </div>

          {/* Additional info */}
          <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded text-xs text-blue-300">
            <div className="font-medium mb-1">💡 How it works:</div>
            <div>
              The bonding curve determines token price based on supply. Early
              buyers get more tokens per HYPE. Total cost includes your input
              amount + 0.001 HYPE pool creation fee.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BondingCurveDisplay;
