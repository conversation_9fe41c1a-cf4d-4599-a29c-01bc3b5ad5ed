'use client';
import BigNumber from 'bignumber.js';

interface BondingCurveDisplayProps {
  tokensToReceive: string;
  totalCost: string;
  baseCost: string;
  platformFee: string;
  poolCreationFee: string;
  isCalculating: boolean;
  error: string | null;
  inputAmount: string;
}

const BondingCurveDisplay = ({
  tokensToReceive,
  totalCost,
  baseCost,
  platformFee,
  poolCreationFee,
  isCalculating,
  error,
  inputAmount,
}: BondingCurveDisplayProps) => {
  // Don't show if no input amount
  if (!inputAmount || inputAmount === '0') {
    return null;
  }

  return (
    <div className="bg-black-700 border border-white-100 rounded-[4px] p-4 space-y-3">
      <div className="text-white-0 text-sm font-medium">
        Purchase Preview
      </div>
      
      {isCalculating ? (
        <div className="text-white-300 text-sm flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white-300"></div>
          Calculating...
        </div>
      ) : error ? (
        <div className="text-red-500 text-sm bg-red-500/10 border border-red-500/20 rounded p-2">
          {error}
        </div>
      ) : (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-white-300 text-sm">
              Tokens to receive:
            </span>
            <span className="text-white-0 text-sm font-medium">
              {BigNumber(tokensToReceive).toFormat(2)} tokens
            </span>
          </div>
          
          <div className="w-full h-[1px] bg-white-100 opacity-30"></div>
          
          <div className="space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-white-300 text-sm">Base cost:</span>
              <span className="text-white-0 text-sm">
                {BigNumber(baseCost).toFormat(4)} HYPE
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-white-300 text-sm">
                Platform fee:
              </span>
              <span className="text-white-0 text-sm">
                {BigNumber(platformFee).toFormat(4)} HYPE
              </span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-white-300 text-sm">
                Pool creation fee:
              </span>
              <span className="text-white-0 text-sm">
                {poolCreationFee} HYPE
              </span>
            </div>
          </div>
          
          <div className="w-full h-[1px] bg-white-100 opacity-30"></div>
          
          <div className="flex justify-between items-center">
            <span className="text-white-0 text-sm font-medium">
              Total cost:
            </span>
            <span className="text-white-0 text-sm font-medium">
              {BigNumber(totalCost).toFormat(4)} HYPE
            </span>
          </div>
          
          {/* Additional info */}
          <div className="mt-3 p-2 bg-blue-500/10 border border-blue-500/20 rounded text-xs text-blue-300">
            <div className="font-medium mb-1">💡 How it works:</div>
            <div>
              The bonding curve determines token price based on supply. Early buyers get more tokens per HYPE.
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BondingCurveDisplay;
