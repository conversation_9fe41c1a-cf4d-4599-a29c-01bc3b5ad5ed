'use client';
import { useState } from 'react';
import { useBondingCurveCalculation } from '@/hooks/useBondingCurveCalculation';
import { AppButton } from '@/components/AppButton';
import BigNumber from 'bignumber.js';

const TestBondingCurve = () => {
  const [testAmount, setTestAmount] = useState<string>('');
  
  const {
    tokensToReceive,
    totalCost,
    baseCost,
    platformFee,
    poolCreationFee,
    isCalculating,
    error,
    calculateFromInputAmount,
  } = useBondingCurveCalculation();

  const testAmounts = ['0.1', '1', '5', '10', '50', '100'];

  const handleTest = async (amount: string) => {
    setTestAmount(amount);
    await calculateFromInputAmount(amount);
  };

  return (
    <div className="min-h-screen bg-black-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-white-0 text-2xl font-bold mb-8">
          Bonding Curve Calculator Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Test Controls */}
          <div className="bg-black-800 border border-white-100 rounded-lg p-6">
            <h2 className="text-white-0 text-lg font-semibold mb-4">
              Test Amounts
            </h2>
            
            <div className="grid grid-cols-3 gap-2 mb-4">
              {testAmounts.map((amount) => (
                <AppButton
                  key={amount}
                  onClick={() => handleTest(amount)}
                  className={`text-sm py-2 ${
                    testAmount === amount 
                      ? 'bg-brand-500' 
                      : 'bg-black-700 hover:bg-black-600'
                  }`}
                >
                  {amount} HYPE
                </AppButton>
              ))}
            </div>
            
            <div className="space-y-2">
              <label className="text-white-300 text-sm">Custom Amount:</label>
              <div className="flex gap-2">
                <input
                  type="number"
                  value={testAmount}
                  onChange={(e) => setTestAmount(e.target.value)}
                  placeholder="Enter HYPE amount"
                  className="flex-1 bg-black-700 border border-white-100 rounded px-3 py-2 text-white-0"
                  step="0.01"
                  min="0"
                  max="1000"
                />
                <AppButton
                  onClick={() => handleTest(testAmount)}
                  className="bg-brand-500 px-4"
                  disabled={!testAmount || testAmount === '0'}
                >
                  Test
                </AppButton>
              </div>
            </div>
          </div>

          {/* Results Display */}
          <div className="bg-black-800 border border-white-100 rounded-lg p-6">
            <h2 className="text-white-0 text-lg font-semibold mb-4">
              Calculation Results
            </h2>
            
            {!testAmount || testAmount === '0' ? (
              <div className="text-white-300 text-sm">
                Select or enter an amount to test the bonding curve calculation.
              </div>
            ) : isCalculating ? (
              <div className="text-white-300 text-sm flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white-300"></div>
                Calculating...
              </div>
            ) : error ? (
              <div className="text-red-500 text-sm bg-red-500/10 border border-red-500/20 rounded p-3">
                <strong>Error:</strong> {error}
              </div>
            ) : (
              <div className="space-y-3">
                <div className="bg-black-700 rounded p-3">
                  <div className="text-white-0 font-medium mb-2">
                    Input: {testAmount} HYPE
                  </div>
                  <div className="text-brand-400 font-medium">
                    Output: {BigNumber(tokensToReceive).toFormat(2)} tokens
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-white-300">Base Cost:</span>
                    <span className="text-white-0">{BigNumber(baseCost).toFormat(6)} HYPE</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white-300">Platform Fee:</span>
                    <span className="text-white-0">{BigNumber(platformFee).toFormat(6)} HYPE</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-white-300">Pool Creation Fee:</span>
                    <span className="text-white-0">{poolCreationFee} HYPE</span>
                  </div>
                  <div className="border-t border-white-100 pt-2 flex justify-between font-medium">
                    <span className="text-white-0">Total Cost:</span>
                    <span className="text-white-0">{BigNumber(totalCost).toFormat(6)} HYPE</span>
                  </div>
                </div>
                
                <div className="bg-blue-500/10 border border-blue-500/20 rounded p-3 text-xs text-blue-300">
                  <div className="font-medium mb-1">Price per token:</div>
                  <div>
                    {BigNumber(totalCost).dividedBy(tokensToReceive || 1).toFormat(8)} HYPE/token
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Formula Explanation */}
        <div className="mt-8 bg-black-800 border border-white-100 rounded-lg p-6">
          <h2 className="text-white-0 text-lg font-semibold mb-4">
            Bonding Curve Formula
          </h2>
          <div className="text-white-300 text-sm space-y-2">
            <div>
              <strong>Tokens Out:</strong> virtualTokenReserves - (virtualHypeReserves × virtualTokenReserves) / (virtualHypeReserves + amountIn)
            </div>
            <div>
              <strong>Base Cost:</strong> (virtualHypeReserves × virtualTokenReserves) / (virtualTokenReserves - tokensOut) - virtualHypeReserves + 1
            </div>
            <div>
              <strong>Platform Fee:</strong> baseCost × platformFeeRate / 10000
            </div>
            <div>
              <strong>Total Cost:</strong> baseCost + platformFee + poolCreationFee (0.001 HYPE)
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestBondingCurve;
